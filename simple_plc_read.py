#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的匯川PLC讀取程式 - 單次讀取版本
"""

from pymodbus.client.sync import ModbusTcpClient

def read_plc_status():
    """讀取PLC M100~M120狀態"""
    
    # PLC連接參數
    PLC_IP = '***********'
    PLC_PORT = 502
    
    # 創建Modbus TCP客戶端
    client = ModbusTcpClient(PLC_IP, port=PLC_PORT)
    
    try:
        # 連接PLC
        if client.connect():
            print(f"成功連接到PLC: {PLC_IP}:{PLC_PORT}")
            
            # 讀取M100~M120 (地址100, 數量21)
            result = client.read_coils(100, 21, unit=1)
            
            if not result.isError():
                print("\nM寄存器狀態:")
                print("-" * 30)
                
                for i, bit in enumerate(result.bits[:21]):
                    address = 100 + i
                    status = "ON" if bit else "OFF"
                    print(f"M{address}: {status}")
                    
            else:
                print(f"讀取錯誤: {result}")
                
        else:
            print(f"無法連接到PLC: {PLC_IP}:{PLC_PORT}")
            
    except Exception as e:
        print(f"發生錯誤: {e}")
        
    finally:
        # 關閉連接
        client.close()

if __name__ == "__main__":
    read_plc_status()
