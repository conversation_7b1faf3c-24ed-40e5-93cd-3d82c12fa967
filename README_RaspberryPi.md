# 樹莓派5B PLC讀取程式

這是一個專為樹莓派5B優化的匯川PLC Modbus TCP讀取程式，用於讀取M100~M120的狀態。

## 系統需求

- 樹莓派5B (或其他樹莓派型號)
- Raspberry Pi OS (推薦最新版本)
- Python 3.7+
- 網路連接到PLC

## 快速開始

### 1. 自動安裝 (推薦)

```bash
# 給腳本執行權限
chmod +x install_dependencies.sh
chmod +x run_plc_reader.sh

# 安裝依賴
bash install_dependencies.sh

# 執行程式
bash run_plc_reader.sh
```

### 2. 手動安裝

```bash
# 更新系統
sudo apt update
sudo apt install -y python3 python3-pip python3-venv build-essential python3-dev

# 創建虛擬環境
python3 -m venv plc_env
source plc_env/bin/activate

# 安裝Python套件
pip install -r requirements.txt

# 執行程式
python3 read_plc_status.py
```

## 程式說明

### 主要功能
- 連接匯川PLC (IP: ***********, Port: 502)
- 讀取M100~M120共21個寄存器狀態
- 每2秒更新一次狀態
- 支援中文顯示

### 樹莓派優化
- 增加連接超時設置 (3秒)
- 自動重試機制
- 降低日誌輸出級別
- 網路連接檢查

## 故障排除

### 1. 無法連接PLC
```bash
# 檢查網路連接
ping ***********

# 檢查端口是否開放
nmap -p 502 ***********
```

### 2. Python套件安裝失敗
```bash
# 更新pip
pip install --upgrade pip

# 安裝系統依賴
sudo apt install -y build-essential python3-dev
```

### 3. 權限問題
```bash
# 給腳本執行權限
chmod +x *.sh
```

## 開機自動啟動 (可選)

如果需要開機自動啟動，可以創建systemd服務：

```bash
# 創建服務文件
sudo nano /etc/systemd/system/plc-reader.service
```

服務文件內容：
```ini
[Unit]
Description=PLC Reader Service
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>/CT_0807
ExecStart=/home/<USER>/CT_0807/run_plc_reader.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

啟用服務：
```bash
sudo systemctl enable plc-reader.service
sudo systemctl start plc-reader.service
```

## 注意事項

1. 確保PLC的Modbus TCP功能已啟用
2. 檢查防火牆設置
3. 樹莓派和PLC需要在同一網段或可路由
4. 建議使用有線網路連接以確保穩定性

## 檔案說明

- `read_plc_status.py` - 主程式
- `simple_plc_read.py` - 簡化版本
- `install_dependencies.sh` - 自動安裝腳本
- `run_plc_reader.sh` - 啟動腳本
- `requirements.txt` - Python依賴清單
