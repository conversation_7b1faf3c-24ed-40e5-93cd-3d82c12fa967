#!/bin/bash
# 樹莓派PLC讀取程式啟動腳本

# 獲取腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "樹莓派5B PLC讀取程式"
echo "====================="

# 檢查虛擬環境是否存在
if [ ! -d "plc_env" ]; then
    echo "虛擬環境不存在，請先執行: bash install_dependencies.sh"
    exit 1
fi

# 激活虛擬環境
source plc_env/bin/activate

# 檢查網路連接
echo "檢查PLC連接..."
if ping -c 1 *********** >/dev/null 2>&1; then
    echo "✓ 可以ping通PLC (***********)"
else
    echo "✗ 無法ping通PLC (***********)"
    echo "請檢查網路連接"
fi

echo ""
echo "啟動PLC讀取程式..."
echo "按 Ctrl+C 停止程式"
echo ""

# 執行程式
python3 read_plc_status.py

# 退出虛擬環境
deactivate
