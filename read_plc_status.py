#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匯川PLC Modbus TCP 讀取程式
讀取M100~M120的狀態
"""

from pymodbus.client.sync import ModbusTcpClient
import time
import sys

class InovancePLCReader:
    def __init__(self, host='***********', port=502):
        """
        初始化PLC連接
        :param host: PLC IP地址
        :param port: Modbus TCP端口
        """
        self.host = host
        self.port = port
        self.client = None
        
    def connect(self):
        """建立與PLC的連接"""
        try:
            self.client = ModbusTcpClient(self.host, port=self.port)
            connection = self.client.connect()
            if connection:
                print(f"成功連接到PLC: {self.host}:{self.port}")
                return True
            else:
                print(f"無法連接到PLC: {self.host}:{self.port}")
                return False
        except Exception as e:
            print(f"連接錯誤: {e}")
            return False
    
    def disconnect(self):
        """斷開與PLC的連接"""
        if self.client:
            self.client.close()
            print("已斷開PLC連接")
    
    def read_m_registers(self, start_address=100, count=21):
        """
        讀取M寄存器狀態
        :param start_address: 起始地址 (M100對應地址100)
        :param count: 讀取數量
        :return: 讀取結果
        """
        try:
            # 匯川PLC的M寄存器通常使用功能碼01 (Read Coils)
            # 地址從0開始，所以M100對應地址100
            result = self.client.read_coils(start_address, count, unit=1)
            
            if result.isError():
                print(f"讀取錯誤: {result}")
                return None
            else:
                return result.bits[:count]  # 只返回需要的位數
                
        except Exception as e:
            print(f"讀取M寄存器時發生錯誤: {e}")
            return None
    
    def display_status(self, status_bits, start_address=100):
        """
        顯示M寄存器狀態
        :param status_bits: 狀態位列表
        :param start_address: 起始地址
        """
        if status_bits is None:
            print("無法獲取狀態")
            return
            
        print("\n" + "="*50)
        print(f"匯川PLC M寄存器狀態 ({time.strftime('%Y-%m-%d %H:%M:%S')})")
        print("="*50)
        
        for i, bit in enumerate(status_bits):
            address = start_address + i
            status = "ON" if bit else "OFF"
            print(f"M{address:03d}: {status}")
        
        print("="*50)

def main():
    """主程式"""
    plc = InovancePLCReader()
    
    try:
        # 連接PLC
        if not plc.connect():
            sys.exit(1)
        
        print("\n開始讀取M100~M120狀態...")
        print("按 Ctrl+C 停止程式")
        
        while True:
            # 讀取M100~M120 (共21個寄存器)
            status = plc.read_m_registers(start_address=100, count=21)
            
            # 顯示狀態
            plc.display_status(status, start_address=100)
            
            # 等待2秒後再次讀取
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n\n程式被用戶中斷")
    except Exception as e:
        print(f"程式執行錯誤: {e}")
    finally:
        plc.disconnect()

if __name__ == "__main__":
    main()
