#!/bin/bash
# 樹莓派5B 依賴安裝腳本

echo "正在為樹莓派5B安裝PLC讀取程式依賴..."

# 更新系統
echo "更新系統套件..."
sudo apt update

# 安裝Python3和pip (通常樹莓派OS已預裝)
echo "檢查Python3和pip..."
sudo apt install -y python3 python3-pip python3-venv

# 安裝系統依賴
echo "安裝系統依賴..."
sudo apt install -y build-essential python3-dev

# 創建虛擬環境 (推薦)
echo "創建Python虛擬環境..."
python3 -m venv plc_env

# 激活虛擬環境並安裝Python套件
echo "安裝Python套件..."
source plc_env/bin/activate
pip install --upgrade pip
pip install pymodbus==2.5.3

echo "安裝完成！"
echo ""
echo "使用方法:"
echo "1. 激活虛擬環境: source plc_env/bin/activate"
echo "2. 執行程式: python3 read_plc_status.py"
echo "3. 退出虛擬環境: deactivate"
